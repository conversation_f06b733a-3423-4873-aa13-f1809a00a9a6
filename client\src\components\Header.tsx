import { useState } from "react";
import { Link, NavLink } from "react-router-dom";
import { Route } from "lucide-react";
import ThemeToggle from "./ThemeToggle";
import { useTheme } from "@/contexts/ThemeContext";

const Header = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { theme } = useTheme();

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className={`${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white'} shadow-sm sticky top-0 z-10`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4 md:justify-start md:space-x-10">
          <div className="flex justify-start lg:w-0 lg:flex-1">
            <Link to="/" className={`text-xl font-bold ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} flex items-center`}>
              <Route className="mr-2" />
              React Router v6
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle />
            <button
              type="button"
              onClick={toggleMobileMenu}
              className={`${theme === 'dark' ? 'text-white hover:text-gray-300' : 'text-gray-700 hover:text-gray-900'} focus:outline-none`}
            >
              <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Desktop navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <NavLink to="/"
              className={({ isActive }) =>
                `relative router-link ${theme === 'dark' ? 'text-gray-300 hover:text-blue-400' : 'text-gray-700 hover:text-blue-600'} px-3 py-2 rounded-md text-sm font-medium
                ${isActive ? (theme === 'dark' ? 'text-blue-400 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-400' : 'text-blue-600 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-600') : ''}`
              }
            >
              Trang Chủ
            </NavLink>
            <NavLink to="/about"
              className={({ isActive }) =>
                `relative router-link ${theme === 'dark' ? 'text-gray-300 hover:text-blue-400' : 'text-gray-700 hover:text-blue-600'} px-3 py-2 rounded-md text-sm font-medium
                ${isActive ? (theme === 'dark' ? 'text-blue-400 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-400' : 'text-blue-600 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-600') : ''}`
              }
            >
              Giới Thiệu
            </NavLink>
            <NavLink to="/contact"
              className={({ isActive }) =>
                `relative router-link ${theme === 'dark' ? 'text-gray-300 hover:text-blue-400' : 'text-gray-700 hover:text-blue-600'} px-3 py-2 rounded-md text-sm font-medium
                ${isActive ? (theme === 'dark' ? 'text-blue-400 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-400' : 'text-blue-600 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-600') : ''}`
              }
            >
              Liên Hệ
            </NavLink>
            <NavLink to="/products"
              className={({ isActive }) =>
                `relative router-link ${theme === 'dark' ? 'text-gray-300 hover:text-blue-400' : 'text-gray-700 hover:text-blue-600'} px-3 py-2 rounded-md text-sm font-medium
                ${isActive ? (theme === 'dark' ? 'text-blue-400 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-400' : 'text-blue-600 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-600') : ''}`
              }
            >
              Sản Phẩm
            </NavLink>
            <NavLink to="/book-manager"
              className={({ isActive }) =>
                `relative router-link ${theme === 'dark' ? 'text-gray-300 hover:text-blue-400' : 'text-gray-700 hover:text-blue-600'} px-3 py-2 rounded-md text-sm font-medium
                ${isActive ? (theme === 'dark' ? 'text-blue-400 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-400' : 'text-blue-600 after:content-[""] after:absolute after:bottom-[-2px] after:left-0 after:w-full after:h-[2px] after:bg-blue-600') : ''}`
              }
            >
              Quản Lý Sách
            </NavLink>
            <ThemeToggle />
          </nav>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`md:hidden ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-t ${mobileMenuOpen ? 'block' : 'hidden'}`}>
        <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
          <NavLink to="/"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium ${theme === 'dark'
                ? `text-gray-300 hover:text-blue-400 hover:bg-gray-700 ${isActive ? 'text-blue-400 bg-gray-700' : ''}`
                : `text-gray-700 hover:text-blue-600 hover:bg-gray-50 ${isActive ? 'text-blue-600 bg-gray-50' : ''}`
              }`
            }
            onClick={() => setMobileMenuOpen(false)}
          >
            Trang Chủ
          </NavLink>
          <NavLink to="/about"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium ${theme === 'dark'
                ? `text-gray-300 hover:text-blue-400 hover:bg-gray-700 ${isActive ? 'text-blue-400 bg-gray-700' : ''}`
                : `text-gray-700 hover:text-blue-600 hover:bg-gray-50 ${isActive ? 'text-blue-600 bg-gray-50' : ''}`
              }`
            }
            onClick={() => setMobileMenuOpen(false)}
          >
            Giới Thiệu
          </NavLink>
          <NavLink to="/contact"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium ${theme === 'dark'
                ? `text-gray-300 hover:text-blue-400 hover:bg-gray-700 ${isActive ? 'text-blue-400 bg-gray-700' : ''}`
                : `text-gray-700 hover:text-blue-600 hover:bg-gray-50 ${isActive ? 'text-blue-600 bg-gray-50' : ''}`
              }`
            }
            onClick={() => setMobileMenuOpen(false)}
          >
            Liên Hệ
          </NavLink>
          <NavLink to="/products"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium ${theme === 'dark'
                ? `text-gray-300 hover:text-blue-400 hover:bg-gray-700 ${isActive ? 'text-blue-400 bg-gray-700' : ''}`
                : `text-gray-700 hover:text-blue-600 hover:bg-gray-50 ${isActive ? 'text-blue-600 bg-gray-50' : ''}`
              }`
            }
            onClick={() => setMobileMenuOpen(false)}
          >
            Sản Phẩm
          </NavLink>
          <NavLink to="/book-manager"
            className={({ isActive }) =>
              `block px-3 py-2 rounded-md text-base font-medium ${theme === 'dark'
                ? `text-gray-300 hover:text-blue-400 hover:bg-gray-700 ${isActive ? 'text-blue-400 bg-gray-700' : ''}`
                : `text-gray-700 hover:text-blue-600 hover:bg-gray-50 ${isActive ? 'text-blue-600 bg-gray-50' : ''}`
              }`
            }
            onClick={() => setMobileMenuOpen(false)}
          >
            Quản Lý Sách
          </NavLink>
        </div>
      </div>
    </header>
  );
};

export default Header;
