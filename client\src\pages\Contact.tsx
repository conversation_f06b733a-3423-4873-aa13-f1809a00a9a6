import { useNavigate } from "react-router-dom";
import { Mail, MapPin, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

const Contact = () => {
  const navigate = useNavigate();
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // This would normally handle form submission
    alert("Cảm ơn về thông điệp của bạn! Đây là biểu mẫu demo.");
  };
  
  return (
    <div className="max-w-4xl mx-auto animate-[fadeIn_0.3s_ease-in-out]">
      <h1 className="text-3xl font-bold mb-6 text-blue-600">Liên Hệ Với Chúng Tôi</h1>
      
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <p className="text-lg mb-6">
          <PERSON><PERSON> câu hỏi về React Router? Chúng tôi ở đây để giúp! Điền vào biểu mẫu bên dưới hoặc sử dụng một trong các kênh liên hệ của chúng tôi.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">Gửi tin nhắn cho chúng tôi</h2>
            <form className="space-y-4" onSubmit={handleSubmit}>
              <div>
                <Label htmlFor="name">Tên</Label>
                <Input 
                  type="text" 
                  id="name" 
                  name="name" 
                  className="mt-1 bg-gray-50" 
                  placeholder="Tên của bạn" 
                />
              </div>
              
              <div>
                <Label htmlFor="email">Email</Label>
                <Input 
                  type="email" 
                  id="email" 
                  name="email" 
                  className="mt-1 bg-gray-50" 
                  placeholder="<EMAIL>" 
                />
              </div>
              
              <div>
                <Label htmlFor="message">Tin nhắn</Label>
                <Textarea 
                  id="message" 
                  name="message" 
                  rows={4} 
                  className="mt-1 bg-gray-50" 
                  placeholder="Tin nhắn của bạn..." 
                />
              </div>
              
              <div>
                <Button 
                  type="submit" 
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Gửi Tin Nhắn
                </Button>
              </div>
            </form>
          </div>
          
          <div>
            <h2 className="text-xl font-semibold mb-4">Kết nối với chúng tôi</h2>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                  <Mail className="h-5 w-5" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900">Email</h3>
                  <p className="text-sm text-gray-500"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                  <MapPin className="h-5 w-5" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900">Địa điểm</h3>
                  <p className="text-sm text-gray-500">123 React Street, JavaScript City</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900">GitHub</h3>
                  <p className="text-sm text-gray-500">github.com/remix-run/react-router</p>
                </div>
              </div>
              
              <div className="flex items-start">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-gray-900">Twitter</h3>
                  <p className="text-sm text-gray-500">@reactrouter</p>
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Tài liệu tham khảo</h3>
              <ul className="space-y-2 text-blue-600">
                <li>
                  <a href="https://reactrouter.com" className="hover:underline flex items-center" target="_blank" rel="noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Tài liệu chính thức
                  </a>
                </li>
                <li>
                  <a href="https://github.com/remix-run/react-router" className="hover:underline flex items-center" target="_blank" rel="noreferrer">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Kho lưu trữ GitHub
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-start">
        <Button 
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium"
          onClick={() => navigate("/")}
        >
          <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Quay lại Trang Chủ
        </Button>
      </div>
    </div>
  );
};

export default Contact;
