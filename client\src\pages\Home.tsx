import { Link } from "react-router-dom";
import { Check<PERSON>ir<PERSON>, ArrowR<PERSON> } from "lucide-react";
import { useTheme } from "@/contexts/ThemeContext";

const Home = () => {
  const { theme } = useTheme();
  return (
    <div className="max-w-7xl mx-auto animate-[fadeIn_0.3s_ease-in-out]">
      <div className="bg-blue-600 rounded-xl p-8 mb-8 text-white">
        <h1 className="text-3xl font-bold mb-4">React Router (v6+)</h1>
        <p className="text-xl">Tìm hiểu cách triển khai định tuyến trong ứng dụng React sử dụng phiên bản React Router mới nhất</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-6`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-800'}`}>Tính năng chính của React Router v6</h2>
          <ul className="space-y-2">
            <li className="flex items-start">
              <CheckCircle className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>API đơn giản hóa với cấu trúc component trực quan hơn</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Định tuyến và liên kết tương đối với lồng route tự động</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Các hook mạnh mẽ như useNavigate, useParams và useLocation</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Cải thiện xử lý các route động và tham số</span>
            </li>
          </ul>
        </div>

        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-6`}>
          <h2 className={`text-xl font-semibold mb-3 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-800'}`}>Những gì bạn sẽ học</h2>
          <ul className="space-y-2">
            <li className="flex items-start">
              <ArrowRight className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Thiết lập React Router trong ứng dụng của bạn</span>
            </li>
            <li className="flex items-start">
              <ArrowRight className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Tạo và quản lý nhiều route</span>
            </li>
            <li className="flex items-start">
              <ArrowRight className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Làm việc với tham số route động</span>
            </li>
            <li className="flex items-start">
              <ArrowRight className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Triển khai route lồng nhau và layouts</span>
            </li>
            <li className="flex items-start">
              <ArrowRight className={`h-5 w-5 ${theme === 'dark' ? 'text-blue-400' : 'text-blue-500'} mt-1 mr-2 flex-shrink-0`} />
              <span className={theme === 'dark' ? 'text-gray-300' : ''}>Điều hướng theo chương trình sử dụng useNavigate</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Installation Section */}
      <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-6 mb-8`}>
        <h2 className={`text-2xl font-bold mb-4 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-800'}`}>Cài đặt & Thiết lập</h2>
        <p className={`mb-4 ${theme === 'dark' ? 'text-gray-300' : ''}`}>Để bắt đầu với React Router v6, cài đặt nó bằng npm hoặc yarn:</p>

        <div className="bg-[#1E293B] rounded-lg p-4 my-4 overflow-x-auto">
          <pre className="font-mono text-[#E2E8F0]">npm install react-router-dom@6</pre>
        </div>

        <p className={`mb-4 mt-6 ${theme === 'dark' ? 'text-gray-300' : ''}`}>Thiết lập cơ bản trong ứng dụng React của bạn:</p>
        <div className="bg-[#1E293B] rounded-lg p-4 my-4 overflow-x-auto">
          <pre className="font-mono text-[#E2E8F0]">{`import { BrowserRouter, Routes, Route } from 'react-router-dom';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/about" element={<About />} />
        <Route path="/contact" element={<Contact />} />
        <Route path="/products/:id" element={<ProductDetail />} />
      </Routes>
    </BrowserRouter>
  );
}`}</pre>
        </div>
      </div>

      {/* V5 vs V6 Comparison */}
      <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-6 mb-8`}>
        <h2 className={`text-2xl font-bold mb-4 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-800'}`}>So sánh React Router v5 với v6</h2>
        <p className={`mb-4 ${theme === 'dark' ? 'text-gray-300' : ''}`}>Những khác biệt chính giữa React Router v5 và v6:</p>

        <div className="overflow-x-auto">
          <table className={`min-w-full divide-y ${theme === 'dark' ? 'divide-gray-700' : 'divide-gray-200'}`}>
            <thead className={theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'}>
              <tr>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>Tính năng</th>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>v5</th>
                <th scope="col" className={`px-6 py-3 text-left text-xs font-medium ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`}>v6</th>
              </tr>
            </thead>
            <tbody className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} divide-y ${theme === 'dark' ? 'divide-gray-700' : 'divide-gray-200'}`}>
              <tr>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>Định nghĩa Route</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>{`<Route component={Home} />`}</code></td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>{`<Route element={<Home />} />`}</code></td>
              </tr>
              <tr>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>Route lồng nhau</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}>Nối đường dẫn thủ công</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}>Tự động với đường dẫn tương đối</td>
              </tr>
              <tr>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>Hook điều hướng</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>useHistory()</code></td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>useNavigate()</code></td>
              </tr>
              <tr>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>Thành phần Switch</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>{`<Switch>`}</code></td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>{`<Routes>`}</code></td>
              </tr>
              <tr>
                <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${theme === 'dark' ? 'text-gray-100' : 'text-gray-900'}`}>Chuyển hướng</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>{`<Redirect>`}</code> component</td>
                <td className={`px-6 py-4 whitespace-nowrap text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-500'}`}><code className={theme === 'dark' ? 'bg-gray-700' : ''}>navigate()</code> hoặc <code className={theme === 'dark' ? 'bg-gray-700' : ''}>{`<Navigate>`}</code></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Components Section */}
      <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-md p-6`}>
        <h2 className={`text-2xl font-bold mb-4 ${theme === 'dark' ? 'text-gray-100' : 'text-gray-800'}`}>Các thành phần cốt lõi</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className={`border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} rounded-lg p-4`}>
            <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} mb-2`}>BrowserRouter</h3>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} mb-2`}>Thành phần gốc cung cấp ngữ cảnh định tuyến cho ứng dụng của bạn.</p>
            <div className="bg-[#1E293B] rounded-lg p-4 my-2 overflow-x-auto">
              <pre className="font-mono text-[#E2E8F0]">{`<BrowserRouter>
  {/* Ứng dụng của bạn với định tuyến */}
</BrowserRouter>`}</pre>
            </div>
          </div>

          <div className={`border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} rounded-lg p-4`}>
            <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} mb-2`}>Routes & Route</h3>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} mb-2`}>Xác định cấu trúc route của ứng dụng.</p>
            <div className="bg-[#1E293B] rounded-lg p-4 my-2 overflow-x-auto">
              <pre className="font-mono text-[#E2E8F0]">{`<Routes>
  <Route path="/" element={<Home />} />
  <Route path="/about" element={<About />} />
</Routes>`}</pre>
            </div>
          </div>

          <div className={`border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} rounded-lg p-4`}>
            <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} mb-2`}>Link</h3>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} mb-2`}>Liên kết điều hướng cập nhật URL mà không làm mới trang.</p>
            <div className="bg-[#1E293B] rounded-lg p-4 my-2 overflow-x-auto">
              <pre className="font-mono text-[#E2E8F0]">{`<Link to="/about">Giới thiệu</Link>
<Link to="/products/123">Xem sản phẩm</Link>`}</pre>
            </div>
          </div>

          <div className={`border ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'} rounded-lg p-4`}>
            <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'} mb-2`}>useNavigate</h3>
            <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} mb-2`}>Hook để điều hướng theo chương trình.</p>
            <div className="bg-[#1E293B] rounded-lg p-4 my-2 overflow-x-auto">
              <pre className="font-mono text-[#E2E8F0]">{`const navigate = useNavigate();

// Điều hướng đến một trang
navigate('/about');

// Quay lại
navigate(-1);`}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
