import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Book } from '@/pages/BookManager';

interface BookListProps {
  books: Book[];
  onEdit: (book: Book) => void;
  onDelete: (id: number) => void;
}

const BookList: React.FC<BookListProps> = ({ books, onEdit, onDelete }) => {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'title' | 'author' | 'year'>('title');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Lọc và sắp xếp sách
  const filteredAndSortedBooks = books
    .filter(book => 
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.year.toString().includes(searchTerm)
    )
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'author':
          comparison = a.author.localeCompare(b.author);
          break;
        case 'year':
          comparison = a.year - b.year;
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

  const handleSort = (field: 'title' | 'author' | 'year') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const getSortIcon = (field: 'title' | 'author' | 'year') => {
    if (sortBy !== field) return '↕️';
    return sortOrder === 'asc' ? '⬆️' : '⬇️';
  };

  return (
    <div className={`${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'} rounded-lg p-6`}>
      <h2 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
        📖 Danh sách sách ({filteredAndSortedBooks.length})
      </h2>

      {/* Tìm kiếm */}
      <div className="mb-4">
        <input
          type="text"
          placeholder="🔍 Tìm kiếm theo tiêu đề, tác giả hoặc năm..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            theme === 'dark' 
              ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400' 
              : 'bg-white border-gray-300 text-gray-900'
          }`}
        />
      </div>

      {/* Sắp xếp */}
      <div className="mb-4 flex flex-wrap gap-2">
        <span className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
          Sắp xếp theo:
        </span>
        <button
          onClick={() => handleSort('title')}
          className={`text-sm px-2 py-1 rounded ${
            sortBy === 'title' 
              ? (theme === 'dark' ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white')
              : (theme === 'dark' ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-700')
          }`}
        >
          Tiêu đề {getSortIcon('title')}
        </button>
        <button
          onClick={() => handleSort('author')}
          className={`text-sm px-2 py-1 rounded ${
            sortBy === 'author' 
              ? (theme === 'dark' ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white')
              : (theme === 'dark' ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-700')
          }`}
        >
          Tác giả {getSortIcon('author')}
        </button>
        <button
          onClick={() => handleSort('year')}
          className={`text-sm px-2 py-1 rounded ${
            sortBy === 'year' 
              ? (theme === 'dark' ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white')
              : (theme === 'dark' ? 'bg-gray-600 text-gray-300' : 'bg-gray-200 text-gray-700')
          }`}
        >
          Năm {getSortIcon('year')}
        </button>
      </div>

      {/* Danh sách sách */}
      {filteredAndSortedBooks.length === 0 ? (
        <div className={`text-center py-8 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
          {books.length === 0 ? (
            <div>
              <p className="text-lg mb-2">📚 Chưa có sách nào</p>
              <p>Hãy thêm sách đầu tiên của bạn!</p>
            </div>
          ) : (
            <div>
              <p className="text-lg mb-2">🔍 Không tìm thấy sách</p>
              <p>Thử tìm kiếm với từ khóa khác</p>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-3">
          {filteredAndSortedBooks.map((book) => (
            <div
              key={book.id}
              className={`p-4 rounded-lg border transition-all hover:shadow-md ${
                theme === 'dark' 
                  ? 'bg-gray-600 border-gray-500 hover:bg-gray-550' 
                  : 'bg-white border-gray-200 hover:bg-gray-50'
              }`}
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className={`font-semibold text-lg mb-1 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
                    📖 {book.title}
                  </h3>
                  <p className={`${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'} mb-1`}>
                    👤 <span className="font-medium">{book.author}</span>
                  </p>
                  <p className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    📅 Năm xuất bản: {book.year}
                  </p>
                </div>
                
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => onEdit(book)}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      theme === 'dark'
                        ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                        : 'bg-yellow-500 hover:bg-yellow-600 text-white'
                    }`}
                    title="Sửa sách"
                  >
                    ✏️ Sửa
                  </button>
                  <button
                    onClick={() => onDelete(book.id)}
                    className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                      theme === 'dark'
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-red-500 hover:bg-red-600 text-white'
                    }`}
                    title="Xóa sách"
                  >
                    🗑️ Xóa
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BookList;
