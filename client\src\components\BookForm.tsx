import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Book } from '@/pages/BookManager';

interface BookFormProps {
  onAdd: (book: Omit<Book, 'id'>) => void;
  onUpdate: (book: Book) => void;
  editingBook: Book | null;
  onCancel: () => void;
}

const BookForm: React.FC<BookFormProps> = ({ onAdd, onUpdate, editingBook, onCancel }) => {
  const { theme } = useTheme();
  const [title, setTitle] = useState('');
  const [author, setAuthor] = useState('');
  const [year, setYear] = useState('');
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Mỗi khi editingBook thay đổi, nạp dữ liệu vào form
  useEffect(() => {
    if (editingBook) {
      setTitle(editingBook.title);
      setAuthor(editingBook.author);
      setYear(editingBook.year.toString());
      setErrors({});
    } else {
      setTitle('');
      setAuthor('');
      setYear('');
      setErrors({});
    }
  }, [editingBook]);

  // Validate form
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!title.trim()) {
      newErrors.title = 'Tiêu đề không được để trống';
    } else if (title.trim().length < 2) {
      newErrors.title = 'Tiêu đề phải có ít nhất 2 ký tự';
    }

    if (!author.trim()) {
      newErrors.author = 'Tác giả không được để trống';
    } else if (author.trim().length < 2) {
      newErrors.author = 'Tên tác giả phải có ít nhất 2 ký tự';
    }

    if (!year.trim()) {
      newErrors.year = 'Năm xuất bản không được để trống';
    } else {
      const yearNum = parseInt(year, 10);
      const currentYear = new Date().getFullYear();
      if (isNaN(yearNum) || yearNum < 1000 || yearNum > currentYear + 1) {
        newErrors.year = `Năm xuất bản phải từ 1000 đến ${currentYear + 1}`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const bookData = {
      title: title.trim(),
      author: author.trim(),
      year: parseInt(year, 10),
    };

    if (editingBook) {
      // Cập nhật sách
      onUpdate({
        ...editingBook,
        ...bookData,
      });
    } else {
      // Thêm sách mới
      onAdd(bookData);
    }

    // Reset form
    setTitle('');
    setAuthor('');
    setYear('');
    setErrors({});
  };

  const handleCancel = () => {
    setTitle('');
    setAuthor('');
    setYear('');
    setErrors({});
    onCancel();
  };

  return (
    <div className={`${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-50'} rounded-lg p-6`}>
      <h2 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
        {editingBook ? '✏️ Sửa Sách' : '➕ Thêm Sách Mới'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Tiêu đề */}
        <div>
          <label className={`block text-sm font-medium mb-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            Tiêu đề sách *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              theme === 'dark' 
                ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400' 
                : 'bg-white border-gray-300 text-gray-900'
            } ${errors.title ? 'border-red-500' : ''}`}
            placeholder="Nhập tiêu đề sách..."
          />
          {errors.title && (
            <p className="text-red-500 text-sm mt-1">{errors.title}</p>
          )}
        </div>

        {/* Tác giả */}
        <div>
          <label className={`block text-sm font-medium mb-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            Tác giả *
          </label>
          <input
            type="text"
            value={author}
            onChange={(e) => setAuthor(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              theme === 'dark' 
                ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400' 
                : 'bg-white border-gray-300 text-gray-900'
            } ${errors.author ? 'border-red-500' : ''}`}
            placeholder="Nhập tên tác giả..."
          />
          {errors.author && (
            <p className="text-red-500 text-sm mt-1">{errors.author}</p>
          )}
        </div>

        {/* Năm xuất bản */}
        <div>
          <label className={`block text-sm font-medium mb-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
            Năm xuất bản *
          </label>
          <input
            type="number"
            value={year}
            onChange={(e) => setYear(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              theme === 'dark' 
                ? 'bg-gray-600 border-gray-500 text-white placeholder-gray-400' 
                : 'bg-white border-gray-300 text-gray-900'
            } ${errors.year ? 'border-red-500' : ''}`}
            placeholder="Nhập năm xuất bản..."
            min="1000"
            max={new Date().getFullYear() + 1}
          />
          {errors.year && (
            <p className="text-red-500 text-sm mt-1">{errors.year}</p>
          )}
        </div>

        {/* Buttons */}
        <div className="flex space-x-3 pt-4">
          <button
            type="submit"
            className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${
              theme === 'dark'
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            {editingBook ? '💾 Cập nhật' : '➕ Thêm sách'}
          </button>
          
          {editingBook && (
            <button
              type="button"
              onClick={handleCancel}
              className={`flex-1 py-2 px-4 rounded-md font-medium transition-colors ${
                theme === 'dark'
                  ? 'bg-gray-600 hover:bg-gray-700 text-white'
                  : 'bg-gray-500 hover:bg-gray-600 text-white'
              }`}
            >
              ❌ Hủy
            </button>
          )}
        </div>
      </form>
    </div>
  );
};

export default BookForm;
