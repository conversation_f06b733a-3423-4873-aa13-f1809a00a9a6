import { useParams, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { products, ProductType } from "@/data/productData";
import { Button } from "@/components/ui/button";
import { Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [product, setProduct] = useState<ProductType | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simulate fetching product data
    setTimeout(() => {
      const foundProduct = products.find(p => p.id === parseInt(id || "0"));
      
      if (foundProduct) {
        setProduct(foundProduct);
      } else {
        toast({
          variant: "destructive",
          title: "Không tìm thấy sản phẩm",
          description: "<PERSON><PERSON><PERSON>ng thể tìm thấy sản phẩm được yêu cầu."
        });
        navigate("/products", { replace: true }); // Redirect if product not found
      }
      
      setLoading(false);
    }, 500);
  }, [id, navigate, toast]);
  
  if (loading) {
    return (
      <div className="max-w-4xl mx-auto flex justify-center items-center py-12">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full bg-blue-200 mb-4"></div>
          <div className="h-4 bg-blue-200 rounded w-32 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-24"></div>
        </div>
      </div>
    );
  }
  
  if (!product) {
    return null; // This should not happen as we redirect on error
  }
  
  return (
    <div className="max-w-4xl mx-auto animate-[fadeIn_0.3s_ease-in-out]">
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <div className="md:flex">
          <div className="md:flex-shrink-0 md:w-1/2">
            <img 
              src={product.image} 
              alt={product.name} 
              className="h-full w-full object-cover md:w-full"
            />
          </div>
          <div className="p-8">
            <div className="flex items-center">
              <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full uppercase font-semibold tracking-wide">
                {product.category}
              </span>
              <div className="ml-2 text-gray-500 text-xs">ID: {product.id}</div>
            </div>
            <h1 className="mt-2 text-2xl font-bold text-gray-900">{product.name}</h1>
            <p className="mt-1 text-xl text-blue-600 font-bold">{product.price}</p>
            
            <div className="mt-4">
              <p className="text-gray-600">{product.description}</p>
            </div>
            
            <div className="mt-6">
              <h2 className="text-lg font-semibold text-gray-900">Tính năng</h2>
              <ul className="mt-2 space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="mt-8 flex justify-between items-center">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white font-bold">
                Thêm vào giỏ hàng
              </Button>
              <Button
                variant="secondary"
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold"
                onClick={() => navigate("/products")}
              >
                Quay lại Sản phẩm
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Triển khai tham số URL</h2>
        <p className="mb-4">
          Trang này minh họa cách React Router v6 xử lý tham số URL. ID sản phẩm hiện tại được trích xuất từ URL bằng hook <code className="bg-gray-100 px-1 py-0.5 rounded">useParams</code>.
        </p>
        
        <div className="bg-[#1E293B] rounded-lg p-4 my-4 overflow-x-auto">
          <pre className="font-mono text-[#E2E8F0]">{`// URL: /products/${id}
import { useParams } from 'react-router-dom';

function ProductDetail() {
  const { id } = useParams();
  // id sẽ bằng "${id}"
  
  // Sau đó, bạn có thể sử dụng ID này để lấy dữ liệu
  useEffect(() => {
    fetchProductData(id);
  }, [id]);
  
  // ...
}`}</pre>
        </div>
        
        <p className="mt-4">
          Tham số URL cho phép bạn tạo các route động có thể hiển thị nội dung khác nhau dựa trên giá trị tham số, hoàn hảo cho các trang sản phẩm, hồ sơ người dùng hoặc bất kỳ nội dung nào tuân theo một mẫu nhất quán với dữ liệu khác nhau.
        </p>
      </div>
    </div>
  );
};

export default ProductDetail;
