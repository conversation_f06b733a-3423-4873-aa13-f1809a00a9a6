import { Link, useNavigate } from "react-router-dom";
import { products } from "@/data/productData";
import { Button } from "@/components/ui/button";

const Products = () => {
  const navigate = useNavigate();
  
  return (
    <div className="max-w-7xl mx-auto animate-[fadeIn_0.3s_ease-in-out]">
      <h1 className="text-3xl font-bold mb-6 text-blue-600">Sản Phẩm</h1>
      
      <div className="mb-8">
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Sản phẩm mẫu</h2>
          <p className="mb-6">
            Trang này minh họa định tuyến động với tham số URL. Nhấp vào sản phẩm để xem chi tiết của nó.
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <div 
                key={product.id}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                <img src={product.image} alt={product.name} className="w-full h-48 object-cover" />
                <div className="p-4">
                  <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
                  <p className="text-gray-600 text-sm mb-2">{product.shortDescription}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-blue-600 font-bold">{product.price}</span>
                    <Button 
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={() => navigate(`/products/${product.id}`)}
                    >
                      Xem Chi Tiết
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Sử dụng tham số Route</h2>
        <p className="mb-4">
          Trong React Router v6, bạn có thể truy cập tham số URL bằng hook <code className="bg-gray-100 px-1 py-0.5 rounded">useParams</code>:
        </p>
        
        <div className="bg-[#1E293B] rounded-lg p-4 my-4 overflow-x-auto">
          <pre className="font-mono text-[#E2E8F0]">{`import { useParams } from 'react-router-dom';

function ProductDetail() {
  const { id } = useParams();
  
  // Lấy dữ liệu sản phẩm dựa trên id
  // ...
  
  return (
    <div>
      <h1>Chi tiết sản phẩm cho sản phẩm {id}</h1>
      {/* Thông tin sản phẩm */}
    </div>
  );
}`}</pre>
        </div>
        
        <p className="mt-4">
          Ví dụ này minh họa cách lấy tham số <code className="bg-gray-100 px-1 py-0.5 rounded">id</code> từ URL như
          <code className="bg-gray-100 px-1 py-0.5 rounded ml-1">/products/123</code>
        </p>
      </div>
      
      <div className="flex justify-start">
        <Button 
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium"
          onClick={() => navigate("/")}
        >
          <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Quay lại Trang Chủ
        </Button>
      </div>
    </div>
  );
};

export default Products;
