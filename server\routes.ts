import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { products as mockProducts } from "../client/src/data/productData";

export async function registerRoutes(app: Express): Promise<Server> {
  // API route to get all products
  app.get("/api/products", (_req: Request, res: Response) => {
    res.json(mockProducts);
  });

  // API route to get a specific product by ID
  app.get("/api/products/:id", (req: Request, res: Response) => {
    const id = parseInt(req.params.id);
    const product = mockProducts.find(p => p.id === id);

    if (product) {
      res.json(product);
    } else {
      res.status(404).json({ error: "Product not found" });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
