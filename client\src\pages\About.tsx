import { useNavigate } from "react-router-dom";
import { History, GitBranch, Rocket, Star } from "lucide-react";
import { Button } from "@/components/ui/button";

const About = () => {
  const navigate = useNavigate();
  
  return (
    <div className="max-w-4xl mx-auto animate-[fadeIn_0.3s_ease-in-out]">
      <h1 className="text-3xl font-bold mb-6 text-blue-600">Giới Thiệu Về React Router</h1>
      
      <div className="bg-white shadow-md rounded-lg p-6 mb-8">
        <p className="text-lg mb-4">
          React Router là thư viện tiêu chuẩn cho định tuyến trong các ứng dụng React. Nó cho phép bạn xây dựng
          ứng dụng web một trang với điều hướng mà không cần làm mới trang khi người dùng điều hướng.
        </p>
        
        <p className="text-lg mb-4">
          React Router sử dụng cấu trúc component để gọi các component, hiển thị thông tin phù hợp.
        </p>
        
        <div className="mt-6">
          <h2 className="text-xl font-semibold mb-3">Lịch sử của React Router</h2>
          <div className="space-y-4">
            <div className="flex">
              <div className="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                <History className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium">Phiên bản đầu (1-3)</h3>
                <p className="text-gray-600">
                  Các triển khai ban đầu về định tuyến cho React, với những cải tiến dần dần trong thiết kế API và khả năng.
                </p>
              </div>
            </div>
            
            <div className="flex">
              <div className="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                <GitBranch className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium">Phiên bản 4</h3>
                <p className="text-gray-600">
                  Viết lại hoàn toàn với việc giới thiệu định tuyến động, nghĩa là định tuyến diễn ra khi ứng dụng của bạn hiển thị.
                </p>
              </div>
            </div>
            
            <div className="flex">
              <div className="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                <Rocket className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium">Phiên bản 5</h3>
                <p className="text-gray-600">
                  Tinh chỉnh API của v4, với các hook cải tiến và tương thích tốt hơn với chế độ concurrent của React.
                </p>
              </div>
            </div>
            
            <div className="flex">
              <div className="flex-shrink-0 h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                <Star className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium">Phiên bản 6</h3>
                <p className="text-gray-600">
                  API đơn giản hóa, hiệu suất cải thiện và phù hợp hơn với các thực hành React hiện đại bao gồm hooks và functional components.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="bg-white shadow-md rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Tính năng trong React Router v6</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 className="font-medium text-blue-600">Route lồng nhau</h3>
            <p className="text-sm">Tạo layouts lồng nhau với mối quan hệ route cha/con</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 className="font-medium text-blue-600">Định tuyến động</h3>
            <p className="text-sm">Xử lý các phân đoạn động với tham số route</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 className="font-medium text-blue-600">Liên kết tương đối</h3>
            <p className="text-sm">Tạo liên kết tương đối với vị trí route hiện tại</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 className="font-medium text-blue-600">Nhiều bố cục Route</h3>
            <p className="text-sm">Tạo bố cục riêng biệt cho các phần khác nhau của ứng dụng</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 className="font-medium text-blue-600">Bộ tải Route</h3>
            <p className="text-sm">Tải dữ liệu trước khi hiển thị route với data routers</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded border border-gray-200">
            <h3 className="font-medium text-blue-600">Form Actions</h3>
            <p className="text-sm">Xử lý gửi biểu mẫu với xử lý hành động tích hợp</p>
          </div>
        </div>
        
        <div className="mt-6">
          {/* Example of programmatic navigation with useNavigate */}
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium"
            onClick={() => navigate("/")}
          >
            <svg className="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Quay lại Trang Chủ
          </Button>
        </div>
      </div>
    </div>
  );
};

export default About;
