import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import BookList from '@/components/BookList';
import BookForm from '@/components/BookForm';

export interface Book {
  id: number;
  title: string;
  author: string;
  year: number;
}

const BookManager: React.FC = () => {
  const { theme } = useTheme();
  const [books, setBooks] = useState<Book[]>([]);
  const [editingBook, setEditingBook] = useState<Book | null>(null);

  // Load dữ liệu từ localStorage khi component mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem('books');
      if (stored) {
        const parsedBooks = JSON.parse(stored);
        setBooks(parsedBooks);
      } else {
        // Dữ liệu mẫu ban đầu nếu chưa có trong localStorage
        const initialBooks: Book[] = [
          { id: 1, title: 'React <PERSON>ơ <PERSON>ản', author: '<PERSON><PERSON><PERSON><PERSON>', year: 2023 },
          { id: 2, title: 'JavaScript Nâng Cao', author: 'Trần Thị B', year: 2022 },
          { id: 3, title: 'TypeScript Thực Chiến', author: 'Lê Văn C', year: 2024 },
        ];
        setBooks(initialBooks);
        localStorage.setItem('books', JSON.stringify(initialBooks));
      }
    } catch (error) {
      console.error('Error loading books from localStorage:', error);
    }
  }, []);

  // Lưu dữ liệu vào localStorage mỗi khi books thay đổi
  useEffect(() => {
    try {
      localStorage.setItem('books', JSON.stringify(books));
    } catch (error) {
      console.error('Error saving books to localStorage:', error);
    }
  }, [books]);

  // Hàm thêm sách mới
  const handleAddBook = (newBook: Omit<Book, 'id'>) => {
    const bookWithId: Book = {
      ...newBook,
      id: Date.now(), // Tạo ID đơn giản bằng timestamp
    };
    setBooks(prevBooks => [...prevBooks, bookWithId]);
  };

  // Hàm cập nhật sách
  const handleUpdateBook = (updatedBook: Book) => {
    setBooks(prevBooks => 
      prevBooks.map(book => 
        book.id === updatedBook.id ? updatedBook : book
      )
    );
    setEditingBook(null); // Thoát chế độ sửa
  };

  // Hàm xóa sách
  const handleDeleteBook = (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa sách này?')) {
      setBooks(prevBooks => prevBooks.filter(book => book.id !== id));
      // Nếu đang sửa sách bị xóa, thoát chế độ sửa
      if (editingBook && editingBook.id === id) {
        setEditingBook(null);
      }
    }
  };

  // Hàm bắt đầu chỉnh sửa sách
  const handleEditClick = (book: Book) => {
    setEditingBook(book);
  };

  // Hàm hủy chỉnh sửa
  const handleCancelEdit = () => {
    setEditingBook(null);
  };

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-gray-100' : 'bg-gray-50 text-gray-900'}`}>
      <div className="container mx-auto px-4 py-8">
        <div className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-lg p-6`}>
          <h1 className={`text-3xl font-bold mb-8 text-center ${theme === 'dark' ? 'text-blue-400' : 'text-blue-600'}`}>
            📚 Quản Lý Sách
          </h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Form thêm/sửa sách */}
            <div>
              <BookForm
                onAdd={handleAddBook}
                onUpdate={handleUpdateBook}
                editingBook={editingBook}
                onCancel={handleCancelEdit}
              />
            </div>

            {/* Danh sách sách */}
            <div>
              <BookList
                books={books}
                onEdit={handleEditClick}
                onDelete={handleDeleteBook}
              />
            </div>
          </div>

          {/* Thống kê */}
          <div className={`mt-8 p-4 rounded-lg ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
            <h3 className={`text-lg font-semibold mb-2 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-800'}`}>
              📊 Thống kê
            </h3>
            <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>
              Tổng số sách: <span className="font-bold">{books.length}</span>
            </p>
            {books.length > 0 && (
              <p className={theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}>
                Năm xuất bản mới nhất: <span className="font-bold">{Math.max(...books.map(book => book.year))}</span>
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookManager;
